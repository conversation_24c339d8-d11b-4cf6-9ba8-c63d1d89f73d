{"name": "ank<PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test-webhooks": "node scripts/test-webhooks.js", "setup-woo-sync": "ts-node scripts/setup-woo-sync.ts", "validate-woo-migration": "ts-node scripts/validate-woo-migration.ts", "dev:woo": "next dev --dotenv .env.woocommerce", "verify-jwt": "node scripts/verify-jwt-auth.mjs", "fix-cart-ids": "node scripts/fix-cart-product-ids.mjs", "test-store-api": "node scripts/test-store-api.js", "test:inventory": "node scripts/test-inventory-system.js", "test:inventory:stock": "node scripts/test-inventory-system.js stock", "test:inventory:reservation": "node scripts/test-inventory-system.js reservation", "test:inventory:webhooks": "node scripts/test-inventory-system.js webhooks", "test:inventory:sse": "node scripts/test-inventory-system.js sse", "test:inventory:cleanup": "node scripts/test-inventory-system.js cleanup", "test:inventory:health": "node scripts/test-inventory-system.js health"}, "dependencies": {"@apollo/client": "^3.13.8", "@emailjs/browser": "^4.1.0", "@formspree/react": "^3.0.0", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@shopify/hydrogen-react": "^2023.10.0", "@shopify/shopify-api": "^7.7.0", "@types/styled-components": "^5.1.34", "@upstash/qstash": "^2.7.23", "@upstash/redis": "^1.34.8", "@woocommerce/woocommerce-rest-api": "^1.0.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "critters": "^0.0.23", "crypto-js": "^4.2.0", "framer-motion": "^10.16.4", "graphql": "^16.11.0", "graphql-request": "^7.2.0", "gsap": "^3.12.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.292.0", "next": "^14.2.24", "next-themes": "^0.2.1", "ngrok": "^5.0.0-beta.2", "node-fetch": "^3.3.2", "razorpay": "^2.9.6", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.5.2", "styled-components": "^6.1.19", "swr": "^2.2.5", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@types/node": "^20.9.4", "@types/react": "^18.2.38", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.21", "dotenv": "^16.3.1", "eslint": "^8.54.0", "eslint-config-next": "14.0.4", "postcss": "^8.5.3", "sharp": "^0.33.5", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5.3.2"}, "config": {"commerceProvider": "woocommerce"}}